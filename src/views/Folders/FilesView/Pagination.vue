<template>
  <div v-if="totalPages > 1" class="flex justify-between items-center px-2 py-4 border-t">
    <!-- 总数信息 -->
    <div class="flex gap-4 items-center text-sm text-muted-foreground">
      <span>{{ currentPage }} / {{ totalPages }} 页</span>

      <!-- 每页显示数量选择 -->
      <div class="flex gap-2 items-center">
        <span>每页显示:</span>
        <Select :model-value="props.pageSize" @update:modelValue="handlePageSizeChange">
          <SelectTrigger class="w-20 h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem :value="20">20</SelectItem>
            <SelectItem :value="50">50</SelectItem>
            <SelectItem :value="100">100</SelectItem>
            <SelectItem :value="200">200</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    <!-- 分页控件 -->
    <div class="flex gap-1 items-center">
      <!-- 首页 -->
      <button :disabled="currentPage === 1 || props.disabled" @click="goToPage(1)"
        class="px-3 py-1 text-sm rounded border hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed">
        首页
      </button>

      <!-- 上一页 -->
      <button :disabled="currentPage === 1 || props.disabled" @click="goToPage(currentPage - 1)"
        class="px-3 py-1 text-sm rounded border hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed">
        <ChevronLeft />
      </button>

      <!-- 页码 -->
      <template v-for="pageNum in visiblePages" :key="pageNum">
        <button v-if="pageNum !== '...'" :disabled="props.disabled" :class="[
          'px-3 py-1 rounded text-sm border',
          pageNum === currentPage
            ? 'bg-primary text-primary-foreground'
            : 'hover:bg-muted'
        ]" @click="goToPage(pageNum as number)">
          {{ pageNum }}
        </button>
        <span v-else class="px-2 text-muted-foreground">...</span>
      </template>

      <!-- 下一页 -->
      <button :disabled="currentPage === totalPages || props.disabled" @click="goToPage(currentPage + 1)"
        class="px-3 py-1 text-sm rounded border hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed">
        <ChevronRight />
      </button>

      <!-- 末页 -->
      <button :disabled="currentPage === totalPages || props.disabled" @click="goToPage(totalPages)"
        class="px-3 py-1 text-sm rounded border hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed">
        末页
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import type { AcceptableValue } from 'reka-ui'
import { ChevronLeft, ChevronRight } from 'lucide-vue-next'

// Props
const props = withDefaults(defineProps<{
  currentPage: number
  pageSize: number
  totalItems: number
  disabled?: boolean
}>(), {
  disabled: false
})

// Emits
const emit = defineEmits<{
  'update:currentPage': [page: number]
  'update:pageSize': [size: number]
  'page-change': [page: number]
  'page-size-change': [size: number]
}>()

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(props.totalItems / props.pageSize)
})

const visiblePages = computed(() => {
  const pages: (number | string)[] = []
  const total = totalPages.value
  const current = props.currentPage
  const maxVisible = 7 // 最多显示7个页码按钮

  if (total <= maxVisible) {
    // 如果总页数小于等于最大显示数，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总是显示第一页
    pages.push(1)

    let startPage: number
    let endPage: number

    if (current <= 3) {
      // 如果当前页在前面，显示 1, 2, 3, 4, ..., total
      startPage = 2
      endPage = 4
      if (endPage < total - 1) {
        pages.push(...Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i))
        pages.push('...')
      }
    } else if (current >= total - 2) {
      // 如果当前页在后面，显示 1, ..., total-3, total-2, total-1, total
      if (total > 4) {
        pages.push('...')
      }
      startPage = total - 3
      endPage = total - 1
      pages.push(...Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i))
    } else {
      // 如果当前页在中间，显示 1, ..., current-1, current, current+1, ..., total
      pages.push('...')
      pages.push(current - 1, current, current + 1)
      if (current + 1 < total - 1) {
        pages.push('...')
      }
    }

    // 总是显示最后一页
    if (total > 1) {
      pages.push(total)
    }
  }

  return pages
})

// 方法
const goToPage = (page: number) => {
  if (props.disabled) return

  if (page >= 1 && page <= totalPages.value && page !== props.currentPage) {
    emit('update:currentPage', page)
    emit('page-change', page)
  }
}

const handlePageSizeChange = (value: AcceptableValue) => {
  if (props.disabled) return

  // 先更新页大小
  emit('update:pageSize', value as number)

  // 重置到第1页
  emit('update:currentPage', 1)

  // 触发事件
  emit('page-change', 1)
  emit('page-size-change', value as number)
}

// 暴露方法给父组件
defineExpose({
  goToPage,
  getTotalPages: () => totalPages.value,
  canGoNext: () => props.currentPage < totalPages.value,
  canGoPrev: () => props.currentPage > 1,
})
</script>