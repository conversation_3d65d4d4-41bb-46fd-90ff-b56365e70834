# Category ID 修复验证测试

## 修复内容总结

### 问题描述
前端文件操作功能（删除、移动、重命名）存在 category_id 获取错误的问题。这些功能使用的 category_id 不是从当前 URL 路由参数中实时获取的，而是使用了缓存的旧值。

### 根本原因
在 `useFileMove.ts`、`useFileDelete.ts` 和 `useRename.ts` 这三个 composables 中，`categoryId` 是在组件初始化时作为静态参数传入的。当用户切换不同分类/文件夹时，虽然路由参数会更新，但已经初始化的 composables 内部仍然使用旧的 `categoryId` 值。

### 修复方案
修改这些 composables 内部直接使用 `useRoute()` 来实时获取当前的 `categoryId`，而不是依赖初始化时传入的静态值。

### 修改的文件

1. **src/composables/useFileMove.ts**
   - 移除 `categoryId?: string` 参数
   - 添加内部 `useRoute()` 和 `computed()` 来实时获取 `categoryId`
   - 更新 API 调用使用 `categoryId.value`

2. **src/composables/useFileDelete.ts**
   - 移除 `categoryId?: string` 参数
   - 添加内部 `useRoute()` 和 `computed()` 来实时获取 `categoryId`
   - 更新 API 调用使用 `categoryId.value`

3. **src/views/Folders/FilesView/useRename.ts**
   - 修改 `useBaseRename` 函数，移除 `categoryId?: string` 参数
   - 添加内部 `useRoute()` 和 `computed()` 来实时获取 `categoryId`
   - 更新 `callRenameAPI` 函数使用 `categoryId.value`
   - 同时更新 `useListViewRename` 和 `useItemRename` 函数

4. **调用方更新**
   - `src/views/Folders/FilesView/ContextMenu.vue`
   - `src/views/Folders/FilesView/ActionButtonGroup.vue`
   - `src/views/Folders/FilesView/index.vue`
   - `src/components/FolderPicker/README.md`

### 验证方法

修复后应该能够在不同分类/文件夹间切换，执行删除、移动、重命名操作时都能正确使用当前分类的 category_id。

#### 测试步骤：
1. 打开应用并导航到某个分类（例如：categoryId=1）
2. 选择一个文件，尝试删除/移动/重命名操作
3. 切换到另一个分类（例如：categoryId=2）
4. 再次选择文件，尝试删除/移动/重命名操作
5. 检查网络请求中的 category_id 参数是否正确对应当前分类

#### 预期结果：
- 在分类1中操作时，API 请求应该包含 `category_id: "1"`
- 在分类2中操作时，API 请求应该包含 `category_id: "2"`
- 不应该出现使用错误 category_id 的情况

### 技术优势
1. **响应式设计**：使用 Vue 3 的 computed 确保 categoryId 始终是最新的
2. **简化调用**：不需要手动传递 categoryId 参数
3. **一致性**：所有文件操作都使用相同的 categoryId 获取方式
4. **可维护性**：减少了参数传递的复杂性

### 符合最佳实践
- 使用 Vue 3 Composition API 的响应式特性
- 遵循单一职责原则
- 减少组件间的耦合度
- 提高代码的可读性和可维护性
